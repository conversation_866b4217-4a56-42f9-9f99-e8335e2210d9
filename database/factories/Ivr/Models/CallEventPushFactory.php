<?php

declare(strict_types=1);

namespace Database\Factories\Ivr\Models;

use App\Ivr\Enums\IvrEvent;
use App\Ivr\Enums\Provider;
use App\Ivr\Models\CallEventPush;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

final class CallEventPushFactory extends Factory
{
    protected $model = CallEventPush::class;

    public function definition(): array
    {
        return [
            'external_id' => $this->faker->uuid(),
            'event' => $this->faker->randomElement(IvrEvent::cases()),
            'provider' => $this->faker->randomElement(Provider::cases()),
            'payload' => [],
            'vendor_id' => $this->faker->numberBetween(1, 100),
            'created_at' => Carbon::now(),
        ];
    }

    /**
     * Configure the factory for completed events.
     */
    public function completed(): self
    {
        return $this->state(static fn (array $attributes) => [
            'completed_at' => Carbon::now(),
            'failed_at' => null,
        ]);
    }

    /**
     * Configure the factory for failed events.
     */
    public function failed(): self
    {
        return $this->state(static fn (array $attributes) => [
            'completed_at' => null,
            'failed_at' => Carbon::now(),
        ]);
    }

    /**
     * Configure the factory for pending events.
     */
    public function pending(): self
    {
        return $this->state(static fn (array $attributes) => [
            'completed_at' => null,
            'failed_at' => null,
        ]);
    }

    /**
     * Configure the factory for outgoing events.
     */
    public function outgoing(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'event' => IvrEvent::Outgoing,
                'payload' => [
                    'caller_number' => $this->faker->numerify('+91##########'),
                    'called_number' => $this->faker->numerify('+91##########'),
                    'call_date' => $this->faker->dateTime()->format('Y-m-d H:i:s'),
                    'total_call_duration' => $this->faker->numberBetween(10, 300),
                    'call_status' => 'completed',
                    'recording_url' => $this->faker->url(),
                ],
            ];
        });
    }

    /**
     * Configure the factory for incoming events.
     */
    public function incoming(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'event' => IvrEvent::Incoming,
                'payload' => [
                    'caller_number' => $this->faker->numerify('+91##########'),
                    'called_number' => $this->faker->numerify('+91##########'),
                    'call_date' => $this->faker->dateTime()->format('Y-m-d H:i:s'),
                    'total_call_duration' => $this->faker->numberBetween(10, 300),
                    'call_status' => 'completed',
                    'recording_url' => $this->faker->url(),
                ],
            ];
        });
    }

    /**
     * Configure the factory with specific vendor ID.
     */
    public function forVendor(int $vendorId): self
    {
        return $this->state(static fn (array $attributes) => [
            'vendor_id' => $vendorId,
        ]);
    }

    /**
     * Configure the factory with specific provider.
     */
    public function withProvider(Provider $provider): self
    {
        return $this->state(static fn (array $attributes) => [
            'provider' => $provider,
        ]);
    }
}
