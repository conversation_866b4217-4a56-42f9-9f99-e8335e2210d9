<?php

declare(strict_types=1);

namespace Database\Factories\Ivr\Models;

use App\Ivr\Enums\Direction;
use App\Ivr\Enums\Provider;
use App\Ivr\Models\Ivr;
use Illuminate\Database\Eloquent\Factories\Factory;

final class IvrFactory extends Factory
{
    protected $model = Ivr::class;

    public function definition(): array
    {
        return [
            'vendor_id' => $this->faker->numberBetween(1, 100),
            'provider' => Provider::GLDialer,
            'called_number' => $this->faker->numerify('91##########'),
            'caller_number' => $this->faker->numerify('91##########'),
            'call_uuid' => $this->faker->uuid(),
            'direction' => $this->faker->randomElement(Direction::cases()),
            'agent_number' => $this->faker->numerify('91##########'),
            'total_call_duration' => $this->faker->numberBetween(10, 3600),
            'call_date' => $this->faker->dateTimeBetween('-1 month')->format('Y-m-d'),
            'call_status' => $this->faker->randomElement(['completed', 'missed', 'busy', 'no-answer']),
            'recording_url' => $this->faker->url(),
            'call_start_time' => $this->faker->dateTime()->format('Y-m-d H:i:s'),
            'call_end_time' => $this->faker->dateTime()->format('Y-m-d H:i:s'),
            'dtmf' => $this->faker->numberBetween(0, 9),
            'conversation_duration' => $this->faker->numberBetween(0, 3600),
            'transferred_number' => $this->faker->optional()->numerify('+91##########'),
            'events' => 'Missed call',
            'caller_name' => $this->faker->name(),
            'fk_int_enquiry_id' => $this->faker->numberBetween(1, 1000),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Configure the factory for inbound calls.
     */
    public function inbound(): self
    {
        return $this->state(static fn (array $attributes) => [
            'direction' => Direction::Inbound,
        ]);
    }

    /**
     * Configure the factory for outbound calls.
     */
    public function outbound(): self
    {
        return $this->state(static fn (array $attributes) => [
            'direction' => Direction::Outbound,
        ]);
    }

    /**
     * Configure the factory for completed calls.
     */
    public function completed(): self
    {
        return $this->state(function (array $attributes) {
            $startTime = now()->subMinutes(rand(5, 60));
            $endTime = $startTime->copy()->addMinutes(rand(1, 30));

            return [
                'call_status' => 'completed',
                'call_start_time' => $startTime,
                'call_end_time' => $endTime,
                'total_call_duration' => $endTime->diffInSeconds($startTime),
                'conversation_duration' => $endTime->diffInSeconds($startTime),
                'recording_url' => $this->faker->url(),
            ];
        });
    }

    /**
     * Configure the factory for missed calls.
     */
    public function missed(): self
    {
        return $this->state(static fn (array $attributes) => [
            'call_status' => 'missed',
            'total_call_duration' => 0,
            'conversation_duration' => 0,
            'recording_url' => null,
        ]);
    }

    /**
     * Configure the factory for calls with an enquiry.
     */
    public function withEnquiry(int $enquiryId): self
    {
        return $this->state(static fn (array $attributes) => [
            'fk_int_enquiry_id' => $enquiryId,
        ]);
    }

    /**
     * Configure the factory for calls with a specific agent.
     */
    public function withAgent(string $agentNumber): self
    {
        return $this->state(static fn (array $attributes) => [
            'agent_number' => $agentNumber,
        ]);
    }
}
