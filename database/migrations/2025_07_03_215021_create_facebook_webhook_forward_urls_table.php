<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('facebook_webhook_forward_urls', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->text('url');
            $table->boolean('status')->default(true);
            $table->timestamps();

            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('facebook_webhook_forward_urls');
    }
};
