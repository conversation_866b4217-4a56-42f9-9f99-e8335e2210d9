<?php

declare(strict_types=1);

namespace Tests\Feature\GLDialer\Http\Controllers;

use App\Ivr\Enums\IvrEvent;
use App\Ivr\Enums\Provider;
use App\Ivr\Models\CallEventPush;
use App\User;
use Illuminate\Support\Str;
use Tests\TestCase;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;

final class CallSyncControllerTest extends TestCase
{
    /**
     * @test
     */
    public function it_should_use_the_correct_middleware(): void
    {
        $this->assertRouteUsesMiddleware('dialer.calls.sync', ['jwt.auth']);
    }

    /**
     * @test
     */
    public function it_should_create_ivr_event_push_records_for_missed_calls(): void
    {
        $user = User::factory()->create();

        $callData = [
            'phone_number' => '************',
            'call_start_at' => '2023-01-01 10:00:00',
            'duration' => 0,
            'event_type' => 'missed',
            'recording_url' => null,
            'direction' => 'inbound',
            'call_id' => $this->faker->uuid(),
        ];

        $this->actingAsWithJWT($user)
            ->postJson(route('dialer.calls.sync'), [
                'data' => [$callData],
            ])
            ->assertAccepted();

        $this->assertDatabaseHas(CallEventPush::class, [
            'vendor_id' => $user->getBusinessId(),
            'external_id' => $callData['call_id'],
            'provider' => Provider::GLDialer->value,
            'event' => IvrEvent::Missed->value,
            'payload' => json_encode([
                'phone_number' => '************',
                'agent_number' => Str::start($user->vchr_user_mobile, '+'),
                'start_at' => '2023-01-01 10:00:00',
                'duration' => 0,
                'recording_url' => null,
                'direction' => 'inbound',
            ]),
        ]);
    }

    /**
     * @test
     */
    public function it_should_create_ivr_event_push_records_for_answered_calls(): void
    {
        // Create a user
        $user = User::factory()->create();

        // Prepare test data
        $callData = [
            [
                'phone_number' => '************',
                'call_start_at' => '2023-01-01 10:00:00',
                'duration' => 120,
                'event_type' => 'answered',
                'recording_url' => 'https://example.com/recording.mp3',
                'direction' => 'inbound',
                'call_id' => 'test-call-id-2',
            ],
        ];

        $this->actingAsWithJWT($user)
            ->postJson(route('dialer.calls.sync'), [
                'data' => $callData,
            ])
            ->assertAccepted();

        // Assert database
        $this->assertDatabaseHas(CallEventPush::class, [
            'vendor_id' => $user->getBusinessId(),
            'external_id' => 'test-call-id-2',
            'provider' => Provider::GLDialer->value,
            'event' => IvrEvent::Answered->value,
            'payload' => json_encode([
                'phone_number' => '************',
                'agent_number' => Str::start($user->vchr_user_mobile, '+'),
                'start_at' => '2023-01-01 10:00:00',
                'duration' => 120,
                'recording_url' => null,
                'direction' => 'inbound',
            ]),
        ]);
    }

    /**
     * @test
     */
    public function it_should_create_ivr_event_push_records_for_outgoing_calls(): void
    {
        // Create a user
        $user = User::factory()->create();

        // Prepare test data
        $callData = [
            [
                'phone_number' => '************',
                'call_start_at' => '2023-01-01 10:00:00',
                'duration' => 60,
                'event_type' => 'outgoing',
                'recording_url' => 'https://example.com/recording.mp3',
                'direction' => 'outbound',
                'call_id' => 'test-call-id-3',
            ],
        ];

        // Make the request
        $response = $this
            ->actingAsWithJWT($user)
            ->postJson(route('dialer.calls.sync'), [
                'data' => $callData,
            ])
            ->assertAccepted();

        // Assert database
        $this->assertDatabaseHas(CallEventPush::class, [
            'vendor_id' => $user->getBusinessId(),
            'external_id' => 'test-call-id-3',
            'provider' => Provider::GLDialer->value,
            'event' => IvrEvent::Outgoing->value,
            'payload' => json_encode([
                'phone_number' => '************',
                'agent_number' => Str::start($user->vchr_user_mobile, '+'),
                'start_at' => '2023-01-01 10:00:00',
                'duration' => 60,
                'recording_url' => 'https://example.com/recording.mp3',
                'direction' => 'outbound',
            ]),
        ]);
    }

    /**
     * @test
     */
    public function it_should_handle_multiple_call_records_in_one_request(): void
    {
        // Create a user
        $user = User::factory()->create();

        // Prepare test data with multiple call records
        $callData = [
            [
                'phone_number' => '************',
                'call_start_at' => '2023-01-01 10:00:00',
                'duration' => 0,
                'event_type' => 'missed',
                'recording_url' => null,
                'direction' => 'inbound',
                'call_id' => 'test-call-id-4',
            ],
            [
                'phone_number' => '919876543211',
                'call_start_at' => '2023-01-01 11:00:00',
                'duration' => 120,
                'event_type' => 'answered',
                'recording_url' => 'https://example.com/recording1.mp3',
                'direction' => 'inbound',
                'call_id' => 'test-call-id-5',
            ],
            [
                'phone_number' => '919876543212',
                'call_start_at' => '2023-01-01 12:00:00',
                'duration' => 60,
                'event_type' => 'outgoing',
                'recording_url' => 'https://example.com/recording2.mp3',
                'direction' => 'outbound',
                'call_id' => 'test-call-id-6',
            ],
        ];

        // Make the request
        $response = $this->actingAsWithJWT($user)
            ->postJson(route('dialer.calls.sync'), [
                'data' => $callData,
            ])
            ->assertAccepted();

        // Assert database has all three records
        $this->assertDatabaseCount('ivr_event_pushes', 3);

        // Check each record exists
        $this->assertDatabaseHas('ivr_event_pushes', [
            'external_id' => 'test-call-id-4',
            'event' => IvrEvent::Missed->value,
        ]);

        $this->assertDatabaseHas('ivr_event_pushes', [
            'external_id' => 'test-call-id-5',
            'event' => IvrEvent::Answered->value,
        ]);

        $this->assertDatabaseHas('ivr_event_pushes', [
            'external_id' => 'test-call-id-6',
            'event' => IvrEvent::Outgoing->value,
        ]);
    }

    protected function actingAsWithJWT($user)
    {
        $token = JWTAuth::fromUser($user);
        $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ]);
        return $this;
    }
}
