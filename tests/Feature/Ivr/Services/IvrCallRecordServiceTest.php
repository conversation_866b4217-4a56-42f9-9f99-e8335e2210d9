<?php

declare(strict_types=1);

namespace Tests\Feature\Ivr\Services;

use App\BackendModel\EnquiryFollowup;
use App\BackendModel\EnquiryType;
use App\CallMaster;
use App\Ivr\Enums\Provider;
use App\Ivr\IVRWebhook\EventPayload;
use App\Ivr\IVRWebhook\ManageEnquiry;
use App\Ivr\Models\Ivr;
use App\Ivr\Services\IvrCallRecordService;
use Carbon\CarbonImmutable;
use Mockery;
use Shared\ValueObjects\PhoneNumber;
use Tests\TestCase;

final class IvrCallRecordServiceTest extends TestCase
{
    private IvrCallRecordService $service;

    private ManageEnquiry $manageEnquiry;

    protected function setUp(): void
    {
        parent::setUp();

        $this->manageEnquiry = Mockery::mock(ManageEnquiry::class);
        $this->service = new IvrCallRecordService($this->manageEnquiry);
    }

    /**
     * @test
     */
    public function it_finds_existing_call_record(): void
    {
        // Arrange
        $callUuid = $this->faker->uuid();
        $vendorId = $this->faker->numberBetween(1, 1000);
        $eventType = 'Incoming call';
        $provider = Provider::Voxbay;

        // Create an existing Ivr record using the factory
        $existingCall = Ivr::factory()->create([
            'call_uuid' => $callUuid,
        ]);

        $eventPayload = $this->createEventPayload();

        // Act
        $result = $this->service->findOrCreateCallRecord($callUuid, $eventPayload, $vendorId, $eventType, $provider);

        // Assert
        $this->assertInstanceOf(Ivr::class, $result);
        $this->assertEquals($callUuid, $result->call_uuid);
        $this->assertEquals($existingCall->id, $result->id);
    }

    /**
     * @test
     */
    public function it_creates_new_call_record_when_none_exists(): void
    {
        // Arrange
        $callUuid = $this->faker->uuid();
        $vendorId = $this->faker->numberBetween(1, 1000);
        $eventType = 'Incoming call';
        $provider = Provider::Voxbay;
        $enquiryId = $this->faker->numberBetween(1, 1000);

        $eventPayload = $this->createEventPayload();

        // Mock the ManageEnquiry to return the enquiry ID
        $this->manageEnquiry->shouldReceive('with')
            ->once()
            ->with(EnquiryType::IVR, $eventPayload->customerPhoneNumber, $vendorId)
            ->andReturn($enquiryId);

        // Act
        $result = $this->service->findOrCreateCallRecord($callUuid, $eventPayload, $vendorId, $eventType, $provider);

        // Assert
        $this->assertInstanceOf(Ivr::class, $result);
        $this->assertEquals($callUuid, $result->call_uuid);
        $this->assertEquals($enquiryId, $result->fk_int_enquiry_id);
        $this->assertEquals($eventType, $result->events);
        $this->assertEquals($vendorId, $result->vendor_id);
        $this->assertEquals($provider, $result->provider);
        $this->assertEquals($eventPayload->calledNumber->toPhoneNumber(), $result->called_number);
        $this->assertEquals($eventPayload->customerPhoneNumber->toPhoneNumber(), $result->caller_number);

        // Check that an EnquiryFollowup was created
        $this->assertDatabaseHas(EnquiryFollowup::class, [
            'enquiry_id' => $enquiryId,
            'note' => $callUuid,
            'name' => $eventType,
            'log_type' => EnquiryFollowup::IVR,
            'response' => CallMaster::VOXBAY,
            'created_by' => $vendorId,
        ]);
    }

    /**
     * @test
     */
    public function it_updates_call_record(): void
    {
        // Arrange
        $callUuid = $this->faker->uuid();
        $eventType = 'Incoming call';

        // Create an Ivr record using the factory
        $call = Ivr::factory()->create([
            'call_uuid' => $callUuid,
        ]);

        $eventPayload = $this->createEventPayload();

        // Act
        $this->service->updateCallRecord($call, $eventPayload, $eventType);

        // Refresh the model from the database
        $call->refresh();

        // Assert using the model instance instead of assertDatabaseHas
        $this->assertEquals($eventType, $call->events);
        $this->assertEquals($eventPayload->duration, $call->total_call_duration);
        $this->assertEquals($eventPayload->dateTime->toDateTimeString(), $call->call_date);
        $this->assertEquals($eventPayload->callStatus, $call->call_status);
        $this->assertEquals($eventPayload->recordingUrl, $call->recording_url);
        $this->assertEquals($eventPayload->startTime, $call->call_start_time);
        $this->assertEquals($eventPayload->endTime, $call->call_end_time);
        $this->assertEquals($eventPayload->conversationDuration, $call->conversation_duration);
    }

    /**
     * Helper method to create a test EventPayload
     */
    private function createEventPayload(): EventPayload
    {
        return new EventPayload(
            customerPhoneNumber: new PhoneNumber('+919876543210'),
            calledNumber: new PhoneNumber('+918765432109'),
            agentPhoneNumber: new PhoneNumber('+917654321098'),
            dateTime: CarbonImmutable::now(),
            callStatus: 'ANSWERED',
            startTime: '10:00:00',
            endTime: '10:05:30',
            conversationDuration: 330,
            duration: 340,
            recordingUrl: $this->faker->url()
        );
    }
}
