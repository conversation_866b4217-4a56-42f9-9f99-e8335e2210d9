<?php

declare(strict_types=1);

namespace Tests\Unit\Ivr\IVRWebhook\Jobs;

use App\BackendModel\EnquiryType;
use App\Ivr\Enums\Direction;
use App\Ivr\Enums\IvrEvent;
use App\Ivr\Integrataions\VoxbayX\VoxbayXOutgoingCallEventParser;
use App\Ivr\Integrataions\VoxbayX\VoxbayXOutgoingEventPayload;
use App\Ivr\IVRWebhook\EventPayload;
use App\Ivr\IVRWebhook\Jobs\IvrProviderEventPayloadParserFactory;
use App\Ivr\IVRWebhook\ParsesProviderEventPayload;
use App\Ivr\IVRWebhook\Jobs\OutGoingCallProcessor;
use App\Ivr\IVRWebhook\ManageEnquiry;
use App\Ivr\Models\Ivr;
use App\Ivr\Models\CallEventPush;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Log;
use Mockery;
use Mockery\MockInterface;
use Shared\ValueObjects\PhoneNumber;
use Tests\LogEntry;
use Tests\LogFake;
use Tests\TestCase;

final class OutGoingCallProcessorTest extends TestCase
{
    use DatabaseTransactions;

    private OutGoingCallProcessor $processor;

    private IvrProviderEventPayloadParserFactory&MockInterface $parserFactory;

    private ManageEnquiry&MockInterface $manageEnquiry;

    protected function setUp(): void
    {
        parent::setUp();

        $this->parserFactory = Mockery::mock(IvrProviderEventPayloadParserFactory::class);
        $this->manageEnquiry = Mockery::mock(ManageEnquiry::class);
        $this->processor = new OutGoingCallProcessor(parserFactory: $this->parserFactory, manageEnquiry: $this->manageEnquiry);

        Log::swap(new LogFake());
    }

    /**
     * @test
     */
    public function it_returns_correct_state(): void
    {
        $this->assertEquals(IvrEvent::Outgoing, $this->processor->state());
    }

    /**
     * @test
     */
    public function it_skips_processing_if_call_record_exists(): void
    {
        $callUuid = $this->faker->uuid();

        Ivr::factory()
            ->outbound()
            ->create([
                'call_uuid' => $callUuid,
            ]);

        $eventPush = CallEventPush::factory()
            ->outgoing()
            ->create([
                'external_id' => $callUuid,
                'vendor_id' => 1,
            ]);

        $this->parserFactory->shouldNotHaveReceived('parse');

        $this->processor->process($eventPush);

        Log::assertLogged(
            static fn (LogEntry $log): bool => $log->level === 'info'
                && $log->message === 'For outgoing call,Existing call record found, skipping'
                && $log->context === [
                    'call_uuid' => $callUuid,
                ]
        );

        $this->assertDatabaseCount(Ivr::class, 1);
    }

    /**
     * @test
     */
    public function it_processes_new_outgoing_call_event(): void
    {
        $callId = $this->faker->uuid();
        $vendorId = $this->faker->numberBetween(1, 100);
        $enquiryId = $this->faker->numberBetween(1, 100);

        $testData = [
            'CalledStatus' => 'ANSWER',
            'CalledDate' => '2025-03-28 14:54:35',
            'TotalCallDuration' => 16,
            'Pickduration' => '2025-03-28 14:54:35',
            'phoneNumber' => '************',
            'calledNumber' => '************',
            'agentPhoneNumber' => '************',
            'RecordingUrl' => 'https://x.voxbay.com:81/callcenter/get_outgoing_callrecordings_without_token/out_90727_************_28032025-145442.wav/2025-03-28/3274',
        ];

        $eventPush = CallEventPush::factory()
            ->outgoing()
            ->forVendor($vendorId)
            ->create([
                'external_id' => $callId,
                'payload' => $testData,
            ]);

        $eventPayload = new EventPayload(
            customerPhoneNumber: new PhoneNumber('+' . $testData['phoneNumber']),
            calledNumber: new PhoneNumber('+' . $testData['calledNumber']),
            agentPhoneNumber: $testData['agentPhoneNumber'],
            callStatus: $testData['CalledStatus'],
            duration: $testData['TotalCallDuration'],
            recordingUrl: $testData['RecordingUrl']
        );

        $parser = Mockery::mock(ParsesProviderEventPayload::class);
        $parser->shouldReceive('parse')
            ->once()
            ->with($eventPush->payload, IvrEvent::Outgoing)
            ->andReturn($eventPayload);

        $this->parserFactory
            ->shouldReceive('getParser')
            ->once()
            ->with($eventPush->provider)
            ->andReturn($parser);

        $this->manageEnquiry
            ->shouldReceive('with')
            ->once()
            ->withArgs(
                static fn (string $type, PhoneNumber $mobileNumber, int $vendorIdArgs) => $type === EnquiryType::IVR
                    && $mobileNumber->equalsTo(new PhoneNumber('+' . $testData['phoneNumber']))
                    && $vendorIdArgs === $vendorId
            )
            ->andReturn($enquiryId);

        $this->processor->process($eventPush);

        $this->assertDatabaseHas(Ivr::class, [
            'vendor_id' => $vendorId,
            'provider' => $eventPush->provider,
            'call_uuid' => $callId,
            'caller_number' => $testData['phoneNumber'],
            'called_number' => $testData['calledNumber'],
            'fk_int_enquiry_id' => $enquiryId,
            'direction' => Direction::Outbound,
            'total_call_duration' => $testData['TotalCallDuration'],
            'call_status' => $testData['CalledStatus'],
            'recording_url' => $testData['RecordingUrl'],
            'agent_number' => $testData['agentPhoneNumber'],
        ]);

        Log::assertLogged(
            static fn (LogEntry $log): bool => $log->level === 'info'
                && $log->message === 'Outgoing call event processed'
                && $log->context === [
                    'enquiry_id' => $enquiryId,
                    'call_uuid' => $callId,
                ]
        );
    }
}
