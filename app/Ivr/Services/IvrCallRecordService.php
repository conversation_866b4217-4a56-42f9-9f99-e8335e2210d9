<?php

declare(strict_types=1);

namespace App\Ivr\Services;

use App\BackendModel\EnquiryFollowup;
use App\BackendModel\EnquiryType;
use App\CallMaster;
use App\Ivr\Enums\Provider;
use App\Ivr\IVRWebhook\EventPayload;
use App\Ivr\IVRWebhook\ManageEnquiry;
use App\Ivr\Models\Ivr;
use Illuminate\Support\Facades\Log;

class IvrCallRecordService
{
    private const NIKSHAN_VENDOR_ID = 1346;

    public function __construct(
        private readonly ManageEnquiry $manageEnquiry
    ) {
    }

    public function findOrCreateCallRecord(
        string $callUuid,
        EventPayload $eventPayload,
        int $vendorId,
        string $eventType,
        Provider $provider
    ): Ivr {
        $call = Ivr::query()
            ->useWritePdo()
            ->where('call_uuid', $callUuid)
            ->first();

        if (! $call instanceof Ivr) {
            Log::info('No existing call record found, creating new records', [
                'call_uuid' => $callUuid,
                'event_type' => $eventType,
            ]);

            $call = $this->createNewCallRecord(
                callUuid: $callUuid,
                eventPayload: $eventPayload,
                vendorId: $vendorId,
                eventType: $eventType,
                provider: $provider
            );
        }

        return $call;
    }

    public function updateCallRecord(Ivr $call, EventPayload $eventPayload, string $eventType): void
    {
        Log::info('Updating existing call record', [
            'call_uuid' => $call->call_uuid,
            'event_type' => $eventType,
        ]);

        $call->total_call_duration = $eventPayload->duration ?? 0;
        $call->call_date = $eventPayload->dateTime?->toDateTimeString();
        $call->call_status = $eventPayload->callStatus;
        $call->recording_url = $eventPayload->recordingUrl;
        $call->call_start_time = $eventPayload->startTime;
        $call->call_end_time = $eventPayload->endTime;
        $call->events = $eventType;
        $call->conversation_duration = $eventPayload->conversationDuration;
        $call->save();
    }

    private function createNewCallRecord(
        string $callUuid,
        EventPayload $eventPayload,
        int $vendorId,
        string $eventType,
        Provider $provider
    ): Ivr {
        $enquiryId = $this->createEnquiry($eventPayload, $vendorId);

        $call = Ivr::query()
            ->create([
                'vendor_id' => $vendorId,
                'provider' => $provider,
                'called_number' => $eventPayload->calledNumber->toPhoneNumber(),
                'caller_number' => $eventPayload->customerPhoneNumber->toPhoneNumber(),
                'call_uuid' => $callUuid,
                'fk_int_enquiry_id' => $enquiryId,
                'events' => $eventType,
                'total_call_duration' => $eventPayload->duration ?? 0,
                'call_date' => $eventPayload->dateTime?->toDateTimeString(),
                'call_status' => $eventPayload->callStatus,
                'recording_url' => $eventPayload->recordingUrl,
                'call_start_time' => $eventPayload->startTime,
                'call_end_time' => $eventPayload->endTime,
                'conversation_duration' => $eventPayload->conversationDuration,
            ]);

        $this->createEnquiryFollowup(
            enquiryId: $enquiryId,
            callUuid: $callUuid,
            vendorId: $vendorId,
            eventType: $eventType
        );

        Log::info('Created new call record', [
            'enquiry_id' => $enquiryId,
            'call_uuid' => $callUuid,
            'event_type' => $eventType,
        ]);

        return $call;
    }

    private function createEnquiry(EventPayload $eventPayload, int $vendorId): int
    {
        $type = $vendorId == self::NIKSHAN_VENDOR_ID
            ? EnquiryType::IVR . '(' . $eventPayload->calledNumber->toPhoneNumber() . ')'
            : EnquiryType::IVR;

        return $this->manageEnquiry->with(
            type: $type,
            mobileNumber: $eventPayload->customerPhoneNumber,
            vendorId: $vendorId
        );
    }

    private function createEnquiryFollowup(int $enquiryId, string $callUuid, int $vendorId, string $eventType): void
    {
        EnquiryFollowup::query()->create([
            'enquiry_id' => $enquiryId,
            'name' => $eventType,
            'note' => $callUuid,
            'log_type' => EnquiryFollowup::IVR,
            'response' => CallMaster::VOXBAY,
            'created_by' => $vendorId,
        ]);
    }
}
