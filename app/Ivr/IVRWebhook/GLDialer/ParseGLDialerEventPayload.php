<?php

declare(strict_types=1);

namespace App\Ivr\IVRWebhook\GLDialer;

use App\Ivr\Enums\IvrEvent;
use App\Ivr\Enums\Provider;
use App\Ivr\IVRWebhook\EventPayload;
use App\Ivr\IVRWebhook\ParsesProviderEventPayload;

class ParseGLDialerEventPayload implements ParsesProviderEventPayload
{
    public function __construct(
        public readonly GLDialerIvrEventParser $parser
    ) {
    }

    public function provider(): Provider
    {
        return Provider::GLDialer;
    }

    public function parse(array $payload, IvrEvent $event): EventPayload
    {
        $glDialerPayload = $this->parser->parse($payload);

        return new EventPayload(
            customerPhoneNumber: $glDialerPayload->customerPhoneNumber,
            calledNumber: $glDialerPayload->agentNumber,
            agentPhoneNumber: $glDialerPayload->agentNumber,
            dateTime: $glDialerPayload->dateTime(),
            callStatus: $event->name,
            startTime: $glDialerPayload->startTime(),
            endTime: $glDialerPayload->endTime(),
            conversationDuration: $glDialerPayload->duration,
            duration: $glDialerPayload->duration,
            recordingUrl: $glDialerPayload->recordingUrl,
        );
    }
}
