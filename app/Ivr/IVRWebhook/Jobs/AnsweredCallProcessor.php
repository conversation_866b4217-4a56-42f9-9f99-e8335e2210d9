<?php

declare(strict_types=1);

namespace App\Ivr\IVRWebhook\Jobs;

use App\Ivr\Enums\IvrEvent;
use App\Ivr\Models\CallEventPush;
use App\Ivr\Services\IvrCallRecordService;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

class AnsweredCallProcessor implements IvrEventProcessor
{
    public function __construct(
        private readonly IvrProviderEventPayloadParserFactory $parserFactory,
        private readonly IvrCallRecordService                 $callRecordService
    )
    {
    }

    public function state(): IvrEvent
    {
        return IvrEvent::Answered;
    }

    public function process(CallEventPush $ivrEventPush): void
    {
        Log::info('Answered call event detected', [
            'vendor_id' => $ivrEventPush->vendor_id,
        ]);

        $eventPayload = $this
            ->parserFactory
            ->getParser($ivrEventPush->provider)
            ->parse($ivrEventPush->payload, $this->state());

        // Find or create call record
        $call = $this->callRecordService
            ->findOrCreateCallRecord(
                callUuid: $ivrEventPush->external_id,
                eventPayload: $eventPayload,
                vendorId: $ivrEventPush->vendor_id,
                eventType: 'Answered call',
                provider: $ivrEventPush->provider
            );

        // Update call record with answered call details
        $this->callRecordService->updateCallRecord(
            call: $call,
            eventPayload: $eventPayload,
            eventType: 'Answered call'
        );

        // Send notification for answered call
        Bus::dispatch(new SendNotificationOnEnquiry(
            new NotificationPayload(
                vendorId: $ivrEventPush->vendor_id,
                enquiryId: $call->fk_int_enquiry_id,
                calledNumber: $eventPayload->calledNumber ?? $call->calledNumber(),
                callerNumber: $eventPayload->customerPhoneNumber,
                callType: 'call_answered',
            )
        ));

        Log::info('Answered call event processed', [
            'enquiry_id' => $call->fk_int_enquiry_id,
            'call_uuid' => $ivrEventPush->external_id,
            'agent_number' => $eventPayload->agentPhoneNumber,
        ]);
    }
}
