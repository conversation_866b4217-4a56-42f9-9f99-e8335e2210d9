<?php

declare(strict_types=1);

namespace App\Ivr\IVRWebhook\Jobs;

use App\Ivr\Enums\IvrEvent;
use App\Ivr\Models\CallEventPush;
use App\Ivr\Services\IvrCallRecordService;
use Exception;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

class IncomingCallProcessor implements IvrEventProcessor
{
    public function __construct(
        private readonly IvrProviderEventPayloadParserFactory $parserFactory,
        private readonly IvrCallRecordService $callRecordService
    ) {
    }

    public function state(): IvrEvent
    {
        return IvrEvent::Incoming;
    }

    /**
     * @throws Exception
     */
    public function process(CallEventPush $ivrEventPush): void
    {
        Log::info('Incoming call event detected', [
            'vendor_id' => $ivrEventPush->vendor_id,
        ]);

        $eventPayload = $this
            ->parserFactory
            ->getParser($ivrEventPush->provider)
            ->parse(payload: $ivrEventPush->payload, event: $this->state());

        // Create call record
        $call = $this->callRecordService
            ->findOrCreateCallRecord(
                callUuid: $ivrEventPush->external_id,
                eventPayload: $eventPayload,
                vendorId: $ivrEventPush->vendor_id,
                eventType: 'Incoming call'
            );

        // Send notification
        Bus::dispatch(new SendNotificationOnEnquiry(
            new NotificationPayload(
                vendorId: $ivrEventPush->vendor_id,
                enquiryId: $call->fk_int_enquiry_id,
                calledNumber: $eventPayload->calledNumber,
                callerNumber: $eventPayload->customerPhoneNumber,
                callType: 'new_call'
            )
        ));

        Log::info('Incoming call event processed', [
            'enquiry_id' => $call->fk_int_enquiry_id,
            'vendor_id' => $ivrEventPush->vendor_id,
        ]);
    }
}
