<?php

declare(strict_types=1);

namespace App\Ivr\IVRWebhook\Jobs;

use App\Ivr\Models\CallEventPush;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Throwable;

final class ProcessCallEventPush implements ShouldQueue
{
    use InteractsWithQueue;
    use SerializesModels;

    public string $queue = 'integration:ivr';

    /**
     * Calculate the number of seconds to wait before retrying the job.
     *
     * @return array<int, int>
     */
    public array $backoff = [60, 90, 180];

    public int $tries = 3;

    public function __construct(
        public readonly CallEventPush $ivrEventPush
    ) {
    }

    /**
     * Get the middleware the job should pass through.
     *
     * @return array<int, object>
     */
    public function middleware(): array
    {
        return [new WithoutOverlapping(key: $this->ivrEventPush->external_id, releaseAfter: 60)];
    }

    /**
     * @throws Exception
     */
    public function handle(IvrEventProcessorFactory $factory): void
    {
        Log::withContext([
            'ivr_event_push_id' => $this->ivrEventPush->id,
            'attempt' => $this->attempts(),
        ])->info('Started processing ivr event push', [
            'payload' => $this->ivrEventPush->payload,
        ]);
        try {
            $factory->getProcessor($this->ivrEventPush->event)
                ->process($this->ivrEventPush);
        } catch (Exception $e) {
            Log::info('Failed to process ivr event push', [
                'ivr_event_push_id' => $this->ivrEventPush->id,
                'attempt' => $this->attempts(),
                'exception' => $e->getMessage(),
                'exception_class' => get_class($e),
            ]);

            $this->ivrEventPush->markAsFailed();
            return;
        }

        $this->ivrEventPush->markAsCompleted();

        Log::info('Finished processing ivr event push');
    }

    public function failed(Throwable $e): void
    {
        Log::info('Failed to process ivr event push', [
            'ivr_event_push_id' => $this->ivrEventPush->id,
            'attempt' => $this->attempts(),
            'exception' => $e->getMessage(),
            'exception_class' => get_class($e),
        ]);

        $this->ivrEventPush->markAsFailed();
    }
}
