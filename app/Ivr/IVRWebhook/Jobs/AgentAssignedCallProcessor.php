<?php

declare(strict_types=1);

namespace App\Ivr\IVRWebhook\Jobs;

use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\Events\CreateFollowup;
use App\Ivr\Enums\IvrEvent;
use App\Ivr\IVRWebhook\EventPayload;
use App\Ivr\Models\Ivr;
use App\Ivr\Models\CallEventPush;
use App\Ivr\Services\IvrCallRecordService;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Shared\ValueObjects\PhoneNumber;

class AgentAssignedCallProcessor implements IvrEventProcessor
{
    public function __construct(
        private readonly IvrProviderEventPayloadParserFactory $parserFactory,
        private readonly IvrCallRecordService $callRecordService
    ) {
    }

    public function state(): IvrEvent
    {
        return IvrEvent::AgentAssigned;
    }

    /**
     * @throws \CuyZ\Valinor\Mapper\MappingError
     */
    public function process(CallEventPush $ivrEventPush): void
    {
        Log::info('Agent assigned call event detected', [
            'vendor_id' => $ivrEventPush->vendor_id,
        ]);

        $eventPayload = $this
            ->parserFactory
            ->getParser($ivrEventPush->provider)
            ->parse($ivrEventPush->payload, $this->state());

        // Create call record
        $call = $this->callRecordService
            ->findOrCreateCallRecord(
                callUuid: $ivrEventPush->external_id,
                eventPayload: $eventPayload,
                vendorId: $ivrEventPush->vendor_id,
                eventType: 'Incoming call'
            );

        Log::withContext([
            'call_uuid' => $ivrEventPush->external_id,
            'agent_number' => $eventPayload->agentPhoneNumber,
            'vendor_id' => $ivrEventPush->vendor_id,
        ])->info('Call assigned to an agent');

        $call->update([
            'agent_number' => $eventPayload->agentPhoneNumber instanceof PhoneNumber
                ? $eventPayload->agentPhoneNumber->toPhoneNumber()
                : $eventPayload->agentPhoneNumber,
            'events' => 'Call assigned to an agent',
        ]);

        Log::info('Starting to assign agent to call enquiry', [
            'agent_number' => $eventPayload->agentPhoneNumber,
        ]);

        if (! $eventPayload->agentPhoneNumber instanceof PhoneNumber) {
            Log::info('Agent number is not a phone number, skipping agent assignment');
            return;
        }

        $agent = User::query()
            ->where('mobile', $eventPayload->agentPhoneNumber->nationalNumber())
            ->where('parent_user_id', $call->vendor_id)
            ->first();

        if (! $agent instanceof User) {
            Log::info('Unable to assign agent to call enquiry as agent not found', [
                'agent_number' => (string) $eventPayload->agentPhoneNumber,
            ]);
            return;
        }

        $enquiry = Enquiry::query()
            ->whereKey($call->fk_int_enquiry_id)
            ->first(['pk_int_enquiry_id', 'staff_id']);

        $isStickAgentEnabled = $this->isStickAgentEnabled($call->vendor_id);

        if ($enquiry->staff_id || ! $isStickAgentEnabled) {
            Log::info(
                'Unable to assign agent to ivr sourced enquiry as stick agent is enabled/staffId already exists',
                [
                    'is_sticky_agent_enabled' => $isStickAgentEnabled,
                    'staff_id' => $enquiry->staff_id,
                ]
            );
            return;
        }

        $enquiry->update([
            'staff_id' => $agent->pk_int_user_id,
            'assigned_date' => Carbon::today(),
        ]);

        Event::dispatch(new CreateFollowup(
            note: $agent->vchr_user_name . ' has been designated as the lead via IVR',
            log_type: EnquiryFollowup::TYPE_ACTIVITY,
            enquiry_id: $enquiry->pk_int_enquiry_id,
            created_by: $ivrEventPush->vendor_id
        ));

        Log::info('Agent assigned', [
            'agent_number' => (string) $eventPayload->agentPhoneNumber,
            'agent_name' => $agent->vchr_user_name,
            'enquiry_id' => $enquiry->pk_int_enquiry_id,
        ]);

        dispatch(new SendNotificationOnEnquiry(
            new NotificationPayload(
                vendorId: $ivrEventPush->vendor_id,
                enquiryId: $enquiry->pk_int_enquiry_id,
                calledNumber: $this->resolveCalledNumber(eventPayload: $eventPayload, ivr: $call),
                callerNumber: $eventPayload->customerPhoneNumber,
                callType: 'call_answered'
            )
        ));
    }

    private function isStickAgentEnabled(int $vendorId): bool
    {
        return (bool) User::query()
            ->select('sticky_agent')
            ->whereKey($vendorId)
            ->value('sticky_agent');
    }

    private function resolveCalledNumber(EventPayload $eventPayload, Ivr $ivr): PhoneNumber
    {
        return $eventPayload->calledNumber instanceof PhoneNumber
            ? $eventPayload->calledNumber
            : new PhoneNumber('+' . $ivr->called_number);
    }
}
