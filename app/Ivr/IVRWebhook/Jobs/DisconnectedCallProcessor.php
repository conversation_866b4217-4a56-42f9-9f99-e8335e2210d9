<?php

declare(strict_types=1);

namespace App\Ivr\IVRWebhook\Jobs;

use App\Ivr\Enums\IvrEvent;
use App\Ivr\Models\Ivr;
use App\Ivr\Models\CallEventPush;
use Illuminate\Support\Facades\Log;
use Shared\ValueObjects\PhoneNumber;

class DisconnectedCallProcessor implements IvrEventProcessor
{
    public function __construct(
        private readonly IvrProviderEventPayloadParserFactory $parserFactory,
    ) {
    }

    public function state(): IvrEvent
    {
        return IvrEvent::Disconnected;
    }

    /**
     * @throws \CuyZ\Valinor\Mapper\MappingError
     */
    public function process(CallEventPush $ivrEventPush): void
    {
        $eventPayload = $this
            ->parserFactory
            ->getParser($ivrEventPush->provider)
            ->parse($ivrEventPush->payload, $this->state());

        Log::info('Call disconnected', [
            'agent_number' => $eventPayload->agentPhoneNumber,
        ]);

        Ivr::query()
            ->where('call_uuid', $ivrEventPush->external_id)
            ->update([
                'agent_number' => $eventPayload->agentPhoneNumber instanceof PhoneNumber
                    ? $eventPayload->agentPhoneNumber->toPhoneNumber()
                    : $eventPayload?->agentPhoneNumber,
                'events' => 'Call Ended',
            ]);
    }
}
