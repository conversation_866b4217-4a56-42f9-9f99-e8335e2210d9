<?php

declare(strict_types=1);

namespace App\Ivr\IVRWebhook\Jobs;

use App\BackendModel\EnquiryType;
use App\Ivr\Enums\Direction;
use App\Ivr\Enums\IvrEvent;
use App\Ivr\IVRWebhook\ManageEnquiry;
use App\Ivr\Models\Ivr;
use App\Ivr\Models\CallEventPush;
use Illuminate\Support\Facades\Log;
use Shared\ValueObjects\PhoneNumber;

class OutGoingCallProcessor implements IvrEventProcessor
{
    public function __construct(
        private readonly IvrProviderEventPayloadParserFactory $parserFactory,
        private readonly ManageEnquiry $manageEnquiry
    ) {
    }

    public function state(): IvrEvent
    {
        return IvrEvent::Outgoing;
    }

    public function process(CallEventPush $ivrEventPush): void
    {
        Log::info('Outgoing call event detected', [
            'vendor_id' => $ivrEventPush->vendor_id,
        ]);

        $call = Ivr::query()
            ->where('call_uuid', $ivrEventPush->external_id)
            ->first();

        if ($call instanceof Ivr) {
            Log::info('For outgoing call,Existing call record found, skipping', [
                'call_uuid' => $ivrEventPush->external_id,
            ]);
            return;
        }

        $eventPayload = $this
            ->parserFactory
            ->getParser($ivrEventPush->provider)
            ->parse($ivrEventPush->payload, $this->state());

        $enquiryId = $this->manageEnquiry->with(
            type: EnquiryType::IVR,
            mobileNumber: $eventPayload->customerPhoneNumber,
            vendorId: $ivrEventPush->vendor_id
        );

        Ivr::query()
            ->create([
                'vendor_id' => $ivrEventPush->vendor_id,
                'provider' => $ivrEventPush->provider,
                'called_number' => $eventPayload->calledNumber->toPhoneNumber(),
                'caller_number' => $eventPayload->customerPhoneNumber->toPhoneNumber(),
                'call_uuid' => $ivrEventPush->external_id,
                'fk_int_enquiry_id' => $enquiryId,
                'events' => 'Outgoing call',
                'direction' => Direction::Outbound,
                'total_call_duration' => $eventPayload->duration ?? 0,
                'call_date' => $eventPayload->dateTime?->toDateTimeString(),
                'call_status' => $eventPayload->callStatus,
                'recording_url' => $eventPayload->recordingUrl,
                'call_start_time' => $eventPayload->startTime,
                'call_end_time' => $eventPayload->endTime,
                'agent_number' => $eventPayload->agentPhoneNumber instanceof PhoneNumber
                    ? $eventPayload->agentPhoneNumber->toPhoneNumber()
                    : $eventPayload->agentPhoneNumber,
                'conversation_duration' => $eventPayload->conversationDuration ?? 0,
            ]);

        Log::info('Outgoing call event processed', [
            'enquiry_id' => $enquiryId,
            'call_uuid' => $ivrEventPush->external_id,
        ]);
    }
}
