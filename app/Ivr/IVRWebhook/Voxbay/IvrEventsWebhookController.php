<?php

declare(strict_types=1);

namespace App\Ivr\IVRWebhook\Voxbay;

use App\BackendModel\EnquiryType;
use App\BackendModel\VirtualNumber;
use App\Common\Facades\Mediator;
use App\Ivr\Enums\IvrEvent;
use App\Ivr\Enums\Provider;
use App\Ivr\IVRWebhook\Jobs\NotifyWhenVirtualNumberNotFound;
use App\Ivr\IVRWebhook\Jobs\ProcessCallEventPush;
use App\Ivr\Models\Ivr;
use App\Ivr\Models\CallEventPush;
use App\Subscription\Usecases\GetSubscription\GetSubscription;
use App\Subscription\Usecases\GetSubscription\Subscription;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Shared\Exceptions\PhoneNumberIsInvalid;

/**
 * Voxbay webhook
 */
final class IvrEventsWebhookController
{
    public function __invoke(IvrEventsWebhookRequest $request): JsonResponse
    {
        Log::withContext([
            'call_id' => (string) $request->string('CallUUID'),
        ])->info('Receiving IVR Calls events from Voxbay - new', [
            'input' => $request->all(),
        ]);

        $vendorId = $this->findVendorId($request);

        if ($vendorId == null) {
            Log::info('Unable to find vendor ID');
            return response()->json([
                'message' => 'Unable to find vendor ID',
                'status' => 'fail',
            ], 404);
        }

        Log::withContext([
            'vendor_id' => $vendorId,
        ]);

        if (! $this->hasValidSubscription($vendorId)) {
            Log::info('Voxbay webhook rejected as unable to found subscription', [
                'vendor_id' => $vendorId,
            ]);

            return response()->json([
                'message' => 'Plan Expired',
                'status' => 'fail',
            ], 202);
        }

        $event = $request->event();

        $ivrEventPush = CallEventPush::query()
            ->create([
                'vendor_id' => $vendorId,
                'external_id' => $request->get('CallUUID'),
                'provider' => Provider::Voxbay,
                'event' => $event,
                'payload' => $request->except(['CallUUID']),
                'created_at' => now(),
            ]);

        if ($event->is(IvrEvent::Unknown)) {
            Log::info('Unknown ivr found and skipping from processing');

            $ivrEventPush->markAsCompleted();

            return response()->json([
                'message' => 'Accepted and processing',
                'status' => 'success',
            ]);
        }

        Bus::dispatch(new ProcessCallEventPush(ivrEventPush: $ivrEventPush));

        return response()->json([
            'message' => 'Accepted and processing',
            'status' => 'success',
        ]);
    }

    private function findVendorId(IvrEventsWebhookRequest $request): ?int
    {
        $ivr = Ivr::query()
            ->useWritePdo()
            ->where('call_uuid', $request->string('CallUUID'))
            ->first(['vendor_id']);

        if ($ivr instanceof Ivr) {
            Log::info('Ivr id found in database,found vendor id', [
                'vendor_id' => $ivr->vendor_id,
                'call_uuid' => $request->string('CallUUID'),
            ]);
            return $ivr->vendor_id;
        }

        Log::info('Ivr id not found in database,looking for virtual number', [
            'call_uuid' => $request->string('CallUUID'),
        ]);

        try {
            $calledNumber = $request->calledNumber();
        } catch (PhoneNumberIsInvalid $e) {
            Log::info('Called number not attached to the payload, So unable to parse it.', [
                'exception' => $e->getMessage(),
            ]);
            return null;
        }

        // Fallback to virtual number lookup
        $virtualNumber = VirtualNumber::query()
            ->where('vchr_virtual_number', $calledNumber->toPhoneNumber())
            ->whereType(EnquiryType::IVR)
            ->first(['fk_int_user_id']);

        if (! $virtualNumber instanceof VirtualNumber) {
            Log::info('Unable to find virtual number', [
                'virtual_number' => $request->calledNumber()->toPhoneNumber(),
            ]);

            dispatch(new NotifyWhenVirtualNumberNotFound(unknownVirtualNumber: $request->calledNumber(), payload: $request->all()));
            return null;
        }

        return $virtualNumber->vendorId();
    }

    private function hasValidSubscription(int $vendorId): bool
    {
        /** @var Subscription $services */
        $services = Mediator::dispatch(new GetSubscription(vendorId: $vendorId));

        return in_array('CRM', $services->services);
    }
}
