<?php

declare(strict_types=1);

namespace App\Ivr\Models;

use App\BackendModel\Enquiry;
use App\BackendModel\VirtualNumber;
use App\Common\Unguarded;
use App\Ivr\Enums\Direction;
use App\Ivr\Enums\Provider;
use App\User;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Shared\ValueObjects\PhoneNumber;

/**
 * 
 *
 * @property int $id
 * @property int|null $vendor_id
 * @property Provider $provider
 * @property string|null $called_number
 * @property string|null $caller_number
 * @property string|null $call_uuid
 * @property Direction $direction
 * @property string|null $agent_number
 * @property int|null $total_call_duration
 * @property string|null $call_date
 * @property string|null $call_status
 * @property string|null $recording_url
 * @property string|null $call_start_time
 * @property string|null $call_end_time
 * @property string|null $dtmf
 * @property int|null $conversation_duration
 * @property string|null $transferred_number
 * @property string|null $events
 * @property string|null $caller_name
 * @property int|null $fk_int_enquiry_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read User|null $agent
 * @property-read User|null $agentb
 * @property-read Enquiry|null $enquiry
 * @property-read mixed $agent_name
 * @property-read mixed $customer_name
 * @property-read VirtualNumber|null $virtual_number
 * @method static \Database\Factories\Ivr\Models\IvrFactory factory($count = null, $state = [])
 * @method static Builder|Ivr newModelQuery()
 * @method static Builder|Ivr newQuery()
 * @method static Builder|Ivr query()
 * @method static Builder|Ivr whereAgentNumber($value)
 * @method static Builder|Ivr whereCallDate($value)
 * @method static Builder|Ivr whereCallEndTime($value)
 * @method static Builder|Ivr whereCallStartTime($value)
 * @method static Builder|Ivr whereCallStatus($value)
 * @method static Builder|Ivr whereCallUuid($value)
 * @method static Builder|Ivr whereCalledNumber($value)
 * @method static Builder|Ivr whereCallerName($value)
 * @method static Builder|Ivr whereCallerNumber($value)
 * @method static Builder|Ivr whereConversationDuration($value)
 * @method static Builder|Ivr whereCreatedAt($value)
 * @method static Builder|Ivr whereDirection($value)
 * @method static Builder|Ivr whereDtmf($value)
 * @method static Builder|Ivr whereEvents($value)
 * @method static Builder|Ivr whereFkIntEnquiryId($value)
 * @method static Builder|Ivr whereId($value)
 * @method static Builder|Ivr whereProvider($value)
 * @method static Builder|Ivr whereRecordingUrl($value)
 * @method static Builder|Ivr whereTotalCallDuration($value)
 * @method static Builder|Ivr whereTransferredNumber($value)
 * @method static Builder|Ivr whereUpdatedAt($value)
 * @method static Builder|Ivr whereVendorId($value)
 * @mixin \Eloquent
 */
final class Ivr extends Model
{
    use Unguarded;
    use HasFactory;

    protected $table = 'ivr';

    protected $appends = ['customer_name'];

    protected $casts = [
        'direction' => Direction::class,
        'provider' => Provider::class,
    ];

    public function enquiry(): HasOne
    {
        return $this->hasOne(Enquiry::class, 'pk_int_enquiry_id', 'fk_int_enquiry_id');
    }

    public function getCustomerNameAttribute()
    {
        return $this->enquiry && $this->enquiry->vchr_customer_name ? $this->enquiry->vchr_customer_name : 'No customer name';
    }

    public function virtual_number()
    {
        return $this->hasOne(VirtualNumber::class, 'vchr_virtual_number', 'called_number');
    }

    public function getAgentNameAttribute()
    {
        return $this->agent ? $this->agent->vchr_user_name : ($this->agentb ? $this->agentb->vchr_user_name : ($this->virtual_number && $this->virtual_number->agent ? $this->virtual_number->agent->vchr_user_name : 'Not Applicable'));
    }

    public function agent()
    {
        return $this->hasOne(User::class, 'vchr_user_mobile', 'agent_number');
    }

    public function agentb()
    {
        return $this->hasOne(User::class, 'mobile', 'agent_number');
    }

    public function calledNumber(): PhoneNumber
    {
        return new PhoneNumber('+' . $this->called_number);
    }
}
