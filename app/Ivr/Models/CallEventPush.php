<?php

declare(strict_types=1);

namespace App\Ivr\Models;

use App\Common\Unguarded;
use App\Ivr\Enums\IvrEvent;
use App\Ivr\Enums\Provider;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property string $external_id
 * @property IvrEvent $event
 * @property Provider $provider
 * @property array $payload
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property \Illuminate\Support\Carbon|null $failed_at
 * @property int $vendor_id
 * @method static \Illuminate\Database\Eloquent\Builder|CallEventPush newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CallEventPush newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CallEventPush query()
 * @method static \Illuminate\Database\Eloquent\Builder|CallEventPush whereEvent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CallEventPush whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CallEventPush whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CallEventPush whereExternalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CallEventPush whereFailedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CallEventPush whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CallEventPush wherePayload($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CallEventPush whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CallEventPush whereVendorId($value)
 * @mixin \Eloquent
 */
final class CallEventPush extends Model
{
    use Unguarded;
    use HasFactory;

    public $table = 'ivr_event_pushes';

    public $timestamps = false;

    protected $casts = [
        'payload' => 'array',
        'created_at' => 'datetime',
        'completed_at' => 'datetime',
        'failed_at' => 'datetime',
        'provider' => Provider::class,
        'event' => IvrEvent::class,
    ];

    public function markAsFailed(): void
    {
        $this->failed_at = Carbon::now();
        $this->save();
    }

    public function markAsCompleted(): void
    {
        $this->completed_at = Carbon::now();
        $this->save();
    }
}
