<?php

namespace App\Http\Controllers\GlCRM\User\Leads;

use App\BackendModel\EnquiryPurpose;
use Illuminate\Http\Request;
use App\FrontendModel\DealAdditionalField;
use App\User;
use App\Http\Controllers\Controller;
use Auth;
use Flash;
use App\DealActivity;

class DealAdditionalFieldsController extends Controller
{
    public function index()
    {
        $enqfields = DealAdditionalField::query()->where('vendor_id', User::getVendorId())->get();
        foreach ($enqfields as $key => $value) {
            $value->values = json_decode($value->values, true);
        }
        return view('gl-crm.pages.user.deals.additional-fields.index')->with('enq_fields', $enqfields);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $input = $request->all();
        $rules = [
            'field_name' => 'required',
        ];

        $rulesMessage = [
            'field_name.required' => 'Field Name required.'

        ];

        $validator = validator($input, $rules, $rulesMessage);

        if ($validator->passes()) {
            $enqfields = new DealAdditionalField();

            $enqfields->field_name = $request->field_name;
            $enqfields->input_type = $request->input_type;
            $enqfields->vendor_id = User::getVendorId();
            $enqfields->created_by = Auth::id();
            if ($request->input_type == 2 || $request->input_type==8) {
                if(!$request->dropdown_values)
                {
                    Flash::error("Whoops! Form Validation Failed ");
                    return back();
                }
                $enqfields->values = json_encode($request->dropdown_values, true);
            }
            if ($request->has('shown_in_filter')) {
                $enqfields->show_in_filter = 1;
            }
            if ($request->has('shown_in_list')) {
                $enqfields->show_in_list = 1;
            }
            if ($request->has('is_required')) {
                $enqfields->is_required = 1;
            }

            $enqfields->save();

            Flash::success("Success ");

            return back();
        } else {
            Flash::error("Whoops! Form Validation Failed ");
            return redirect()->back()->withErrors($validator)->withInput();

        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $enqfields = DealAdditionalField::where('vendor_id', User::getVendorId())->get();
        foreach ($enqfields as $key => $value) {
            $value->values = json_decode($value->values, true);
        }
        $field = DealAdditionalField::find($id);

        if ($field && $field->values != NULL) {
            $field->values = json_decode($field->values, true);
        }
        return view('gl-crm.pages.user.deals.additional-fields.show')
            ->with('enq_fields', $enqfields)
            ->with('enq_field', $field);

    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public
    function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public
    function update(Request $request, $id)
    {
        $enqfields = DealAdditionalField::find($id);
        $enqfields->field_name = $request->field_name;
        $enqfields->input_type = $request->input_type;
        if ($enqfields->input_type == 2 || $request->input_type==8) {
            if(!$request->dropdown_values)
                {
                    Flash::error("Whoops! Form Validation Failed ");
                    return back();
                }
            $enqfields->values = json_encode($request->dropdown_values, true);
        }else{
            $enqfields->values = NULL;
        }

        if ($request->has('shown_in_filter')) {
            $enqfields->show_in_filter = 1;
        } else {
            $enqfields->show_in_filter = 0;
        }
        if ($request->has('shown_in_list')) {
            $enqfields->show_in_list = 1;
        } else {
            $enqfields->show_in_list = 0;
        }

        if ($request->is_required == 'on') {
            $enqfields->is_required = 1;
        } else {
            $enqfields->is_required = 0;
        }
        $enqfields->save();
    
        return redirect('user/additional-fields-deal/');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public
    function destroy($id)
    {
        $enqfields = DealAdditionalField::find($id);
        if($enqfields->additionalDetails()->exists()){
            return response()->json(['msg' => "Additional field could not be deleted,Deal exists", 'status' => 'fail']);
        }
        if ($enqfields) {
            $flag = $enqfields->delete();
            if ($flag) {
                return response()->json(['msg' => "Deleted Successfully.", 'status' => 'success']);
            } else {
                return response()->json(['msg' => "Field could not be deleted.", 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => "Field not found.", 'status' => 'fail']);
        }
    }
}
