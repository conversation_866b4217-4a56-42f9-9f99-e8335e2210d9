<?php

namespace App\Http\Controllers\User;

use App\AgentStaff;
use App\BackendModel\Enquiry;
use App\BackendModel\GlApiTokens;
use App\BackendModel\IvrTransfer;
use App\BackendModel\IvrTransferLog;
use App\BackendModel\VirtualNumber;
use App\CallMaster;
use App\CloudTelephonySetting;
use App\Common\Variables;
use App\Http\Controllers\Controller;
use App\Ivr\Enums\Direction;
use App\Ivr\Models\Ivr;
use App\IvrExtension;
use App\User;
use Carbon\Carbon;
use DataTables;
use DB;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Auth;
use Response;
use Validator;

class IvrController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        /**
         * @var User $user
         */
        $user = Auth::user();

        $vendorId = $user->getBusinessId();

        if ($user->isOpsStaff()) {
            $staffs = User::query()
                ->where('pk_int_user_id', $user->pk_int_user_id)
                ->orWhereIn('pk_int_user_id', AgentStaff::query()
                    ->where('agent_id', $user->pk_int_user_id)
                    ->pluck('staff_id'))
                ->get();
        } else {
            $staffs = User::query()
                ->where('pk_int_user_id', $vendorId)
                ->orWhere('parent_user_id', $vendorId)
                ->get();
        }


        $telephony = CloudTelephonySetting::query()
            ->select(['operator'])
            ->where('default', 1)
            ->where('vendor_id', $vendorId)
            ->first();

        if ($telephony instanceof CloudTelephonySetting && $telephony->operator == 2) {
            $statuses = CallMaster::query()
                ->where('vendor_id', $vendorId)
                ->distinct()
                ->whereNotNull('call_status')
                ->get(['call_status']);

            $did = CallMaster::query()
                ->where('vendor_id', $vendorId)
                ->distinct()
                ->where(function ($q) {
                    $q->whereNotNull('call_status')
                        ->whereNotNull('did')
                        ->whereNotIn('did', [2147483647]);
                })->get(['did']);
        } else {
            $statuses = Ivr::query()->where('vendor_id', $vendorId)->distinct()->get(['call_status']);
            $did = Ivr::query()->where('vendor_id', $vendorId)->distinct()->get(['called_number as did']);
        }

        return view('backend.user-pages.ivr.ivr-index', compact('staffs', 'statuses', 'did'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Ivr\Models\Ivr $iVR
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $ivr = Ivr::find($id);
        return view('backend.user-pages.ivr.ivr-show', compact('ivr'));

    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Ivr\Models\Ivr $iVR
     * @return \Illuminate\Http\Response
     */
    public function edit(Ivr $iVR)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Ivr\Models\Ivr $iVR
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Ivr $iVR)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Ivr\Models\Ivr $iVR
     * @return \Illuminate\Http\Response
     */
    public function destroy(Ivr $iVR)
    {
        //
    }

    public function gerIvr(Request $request)
    {
        /**
         * @var User $user
         */
        $user = Auth::user();
        $userId = $user->pk_int_user_id;
        $vendorId = $user->getBusinessId();

        if (!$request->export) {
            $columns = ['id', '', 'called_number', 'caller_number', '', 'created_at', 'call_status', '', ''];
            $draw = $request->draw;
            $start = $request->start; //Start is the offset
            $length = $request->length; //How many records to show
            $column = $request->order[0]['column']; //Column to orderBy
            $dir = $request->order[0]['dir']; //Direction of orderBy
            $searchValue = $request->search['value']; //Search value

            //Sets the current page
            Paginator::currentPageResolver(function () use ($start, $length) {
                return ($start / $length + 1);
            });
        } else {
            $start = 0;
            $length = 0;
        }

        $telephony = CloudTelephonySetting::query()
            ->where('default', 1)
            ->where('vendor_id', $vendorId)
            ->first();

        if ($telephony) {
            if ($telephony->viaBonvoice()) {
                $callMaster = CallMaster::query()
                    ->where('vendor_id', $vendorId)
                    ->whereNotNull('call_status')
                    ->with(['virtual_number', 'virtual_number.agent', 'agent'])
                    ->where(function ($q) use ($request) {
                        if ($request->call_status) {
                            $q->where('call_status', $request->call_status);
                        }
                        if ($request->call_type && $request->call_type == 'Outgoing') {
                            $q->where('direction', 'Outbound');
                        }
                        if ($request->call_type && $request->call_type != 'Outgoing') {
                            $q->where('direction', 'Inbound');
                        }
                        if ($request->did_filter) {
                            $q->where(function ($query) use ($request) {
                                $query->where('did', 'LIKE', '%' . $request->did_filter . '%');
                            });
                        }
                        if (auth()->user()->int_role_id == Variables::STAFF && auth()->user()->is_co_admin == 0) {
                            $q->where(function ($where) use ($request) {
                                $user = auth()->user();
                                $where->where('called_number', 'like', "%" . $user->mobile)->orWhere('caller_number', "%" . $user->mobile);
                                $assignedStafId = AgentStaff::where('agent_id', $user->pk_int_user_id)->pluck('staff_id')->toArray();
                                if (count($assignedStafId) > 0)
                                    foreach ($assignedStafId as $key => $value) {
                                        $usr = User::select('mobile')->find($value);
                                        if ($usr)
                                            $where->orWhere('called_number', 'like', "%" . $usr->mobile)
                                                ->orWhere('caller_number', "%" . $usr->mobile);
                                    }
                            });
                        } else {
                            if ($request->staff_id) {
                                $q->where(function ($where) use ($request) {
                                    $user = User::select('mobile')->find($request->staff_id);
                                    $where->where('called_number', 'like', "%" . $user->mobile)->orWhere('caller_number', "%" . $user->mobile);
                                });
                            }
                        }
                        if ($request->created_at_from) {
                            $q->where('created_at', '>=', Carbon::parse($request->created_at_from)->format('Y-m-d') . ' 00:00:00');
                        }

                        if ($request->created_at_to) {
                            $q->where('created_at', '<=', Carbon::parse($request->created_at_to)->format('Y-m-d') . ' 23:59:59');
                        }
                    });

                if (!$request->export) {
                    $callMaster->where(function ($where) use ($searchValue) {
                        if ($searchValue != '') {
                            $where->where('called_number', 'like', '%' . $searchValue . '%')
                                ->orWhere('caller_number', 'like', '%' . $searchValue . '%')
                                ->orWhere('caller_name', 'like', '%' . $searchValue . '%')
                                ->orWhere('call_masters.created_at', 'like', '%' . $searchValue . '%')
                                ->orWhere('call_status', 'like', '%' . $searchValue . '%');
                        }
                    })->orderBy($columns[$column] ?? 'call_masters.created_at', $dir);
                    $data = $callMaster->paginate($length);
                } else {
                    $data = $callMaster->get();
                }
            } else {
                $ivr = Ivr::query()
                    ->where('vendor_id', $vendorId)
                    ->with(['virtual_number', 'virtual_number.agent', 'agent'])
                    ->where(function ($q) use ($userId, $user, $request) {
                        if ($request->call_status) {
                            $q->where('call_status', $request->call_status);
                        }
                        if ($request->call_type && $request->call_type == 'Outgoing') {
                            $q->where('direction', Direction::Outbound);
                        }
                        if ($request->call_type && $request->call_type != 'Outgoing') {
                            $q->where('direction', Direction::Inbound);
                        }
                        if ($request->did_filter) {
                            $q->where('called_number', $request->did_filter);
                        }
                        if ($user->isOpsStaff()) {
                            $assignedStafId = AgentStaff::query()->where('agent_id', $userId)->pluck('staff_id')->toArray();
                            $q->where(function ($where) use ($userId, $request, $assignedStafId) {
                                $where->whereHas('agent', function ($where) use ($userId, $request, $assignedStafId) {
                                    $where->where('pk_int_user_id', $userId)->orWhereIn('pk_int_user_id', $assignedStafId);
                                })->orWhereHas('virtual_number', function ($where) use ($userId, $request, $assignedStafId) {
                                    $where->where('agent_id', $userId)->orWhereIn('pk_int_user_id', $assignedStafId);
                                });
                            });
                        } else {
                            if ($request->staff_id) {
                                $q->where(function ($where) use ($request) {
                                    $where->whereHas('agent', function ($where) use ($request) {
                                        $where->where('pk_int_user_id', $request->staff_id);
                                    })->orWhereHas('virtual_number', function ($where) use ($request) {
                                        $where->where('agent_id', $request->staff_id);
                                    });
                                });
                            }
                        }

                        if ($request->created_at_from) {
                            $q->where('created_at', '>=', Carbon::parse($request->created_at_from)->format('Y-m-d') . ' 00:00:00');
                        }

                        if ($request->created_at_to) {
                            $q->where('created_at', '<=', Carbon::parse($request->created_at_to)->format('Y-m-d') . ' 23:59:59');
                        }
                    });

                if (!$request->export) {
                    $ivr->where(function ($where) use ($searchValue) {
                        if ($searchValue != '') {
                            $where->where('called_number', 'like', '%' . $searchValue . '%')
                                ->orWhere('caller_number', 'like', '%' . $searchValue . '%')
                                ->orWhere('caller_name', 'like', '%' . $searchValue . '%')
                                ->orWhere('created_at', 'like', '%' . $searchValue . '%')
                                ->orWhere('call_status', 'like', '%' . $searchValue . '%');
                        }
                    })->orderBy($columns[$column] ?? 'created_at', $dir);
                    $data = $ivr->paginate($length);
                } else {
                    $data = $ivr->get();
                }
            }
        } else {
            $ivr = Ivr::query()
                ->where('vendor_id', $vendorId)
                ->with(['virtual_number', 'virtual_number.agent', 'agent'])
                ->where(function ($q) use ($request) {
                    if ($request->call_status) {
                        $q->where('call_status', $request->call_status);
                    }
                    if ($request->call_type && $request->call_type == 'Outgoing') {
                        $q->where('direction', Direction::Outbound);
                    }
                    if ($request->call_type && $request->call_type != 'Outgoing') {
                        $q->where('direction', Direction::Inbound);
                    }
                    if ($request->did_filter) {
                        $q->where('called_number', 'LIKE', '%' . $request->did_filter . '%');
                    }
                    if (Auth::user()->int_role_id == Variables::STAFF && Auth::user()->is_co_admin == 0) {
                        $assignedStafId = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)->pluck('staff_id')->toArray();
                        $q->where(function ($where) use ($request, $assignedStafId) {
                            $where->whereHas('agentb', function ($where) use ($request, $assignedStafId) {
                                $where->where('pk_int_user_id', Auth::user()->pk_int_user_id)->orWhereIn('pk_int_user_id', $assignedStafId);
                            })->orWhereHas('virtual_number', function ($where) use ($request, $assignedStafId) {
                                $where->where('agent_id', Auth::user()->pk_int_user_id)->orWhereIn('pk_int_user_id', $assignedStafId);
                            });
                        });
                    } else {
                        if ($request->staff_id) {
                            $q->where(function ($where) use ($request) {
                                $where->whereHas('agentb', function ($where) use ($request) {
                                    $where->where('pk_int_user_id', $request->staff_id);
                                })->orWhereHas('virtual_number', function ($where) use ($request) {
                                    $where->where('agent_id', $request->staff_id);
                                });
                            });
                        }
                    }
                    if ($request->created_at_from) {
                        $q->where('created_at', '>=', Carbon::parse($request->created_at_from)->format('Y-m-d') . ' 00:00:00');
                    }

                    if ($request->created_at_to) {
                        $q->where('created_at', '<=', Carbon::parse($request->created_at_to)->format('Y-m-d') . ' 23:59:59');
                    }
                });
            if (!$request->export) {
                $ivr->where(function ($where) use ($searchValue) {
                    if ($searchValue != '') {
                        $where->where('called_number', 'like', '%' . $searchValue . '%')
                            ->orWhere('caller_number', 'like', '%' . $searchValue . '%')
                            ->orWhere('caller_name', 'like', '%' . $searchValue . '%')
                            ->orWhere('created_at', 'like', '%' . $searchValue . '%')
                            ->orWhere('call_status', 'like', '%' . $searchValue . '%');
                    }
                })->orderBy($columns[$column] ?? 'created_at', $dir);

                $data = $ivr->paginate($length);
            } else {
                $data = $ivr->get();
            }
        }

        foreach ($data as $key => $row) {
            $row->slno = $start + $key + 1;
            $row->ivr_number = '+' . $row->called_number;
            $row->cust_number = (in_array(auth()->user()->pk_int_user_id, Variables::IVR_NUMBER_RESTRICT)) ? '+' . '********' . substr($row->caller_number, -2) : '+' . $row->caller_number;
            $row->call_date = Carbon::parse($row->created_at)->format('d M Y h:i A');

            if ($telephony) {
                if ($telephony->operator == 2) {
                    $enq = Enquiry::query()
                        ->select('pk_int_enquiry_id', 'fk_int_user_id', 'vchr_customer_name', 'vchr_customer_mobile')
                        ->where('fk_int_user_id', $vendorId)
                        ->where('vchr_customer_mobile', $row->caller_number)
                        ->first();

                    if ($enq) {
                        if (!$request->export)
                            $row->caller_name = '<a href="javascript:showEditModal(' . $enq->pk_int_enquiry_id . ',\'' . $enq->vchr_customer_name . '\')">' . ($enq->vchr_customer_name ? $enq->vchr_customer_name : 'No Customer Name') . '</a>';
                        else
                            $row->caller_name = $enq->vchr_customer_name ?? 'No Name';
                    } else {
                        $row->caller_name = 'Lead Not Found';
                    }

                    if ($row->direction == 'Inbound') {
                        $user = User::active()->where('mobile', substr($row->called_number, -10))->first();
                        $row->agent_name = $user ? $user->vchr_user_name : '';
                    } elseif ($row->direction == 'Outbound') {
                        $user = User::active()->where('mobile', substr($row->called_number, -10))->first();
                        $row->agent_name = $user ? $user->vchr_user_name : '';
                    } else {
                        $row->agent_name = '';
                    }

                    $rec_url = $row->recording_url;
                    $row->source_type = ($row->direction == 'Outbound') ? 'Outgoing Call' : 'Incoming Call';
                    $row->action = '<div class="dropdown show">
                                        <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <i class="fa fa-circle" aria-hidden="true"></i>
                                            <i class="fa fa-circle" aria-hidden="true"></i>
                                            <i class="fa fa-circle" aria-hidden="true"></i>   
                                        </a>
                                        <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                           
                                            <a href="' . $row->recording_url . '" class="dropdown-item" >
                                                <i class="fa fa-download mg-r-5"></i>&nbsp;Download Record Audio
                                            </a>
                                        </div>
                                    </div>';
                } else {
                    if (!$request->export) {
                        $row->caller_name = '<a href="javascript:showEditModal(' . $row->fk_int_enquiry_id . ',\'' . $row->caller_name . '\')">' . $row->caller_name . '</a>';
                    } else {
                        $row->caller_name = $row->caller_name;
                    }

                    $rec_url = $row->recording_url ? $this->resolveVoxBayRecordingUrl($row->recording_url) : null;
                    $row->source_type = $row->direction->is(Direction::Outbound) ? 'Outgoing Call' : 'Incoming Call';

                    $row->agent_name = $row->agent_name;
                    $row->action = '
                    <a href="' . $rec_url . '" class="btn btn-info" title="Download Record Audio" download ><i class="fa fa-download mg-r-5"></i></a>';
                }
            } else {
                if (!$request->export)
                    $row->caller_name = '<a href="javascript:showEditModal(' . $row->fk_int_enquiry_id . ',\'' . $row->customer_name . '\')">' . $row->customer_name . '</a>';
                else
                    $row->caller_name = $row->customer_name;
                $rec_url = str_replace('https://pbx.voxbaysolutions.com/callrecordings/', '', $row->recording_url);
                $row->source_type = $row->direction->is(Direction::Outbound) ? 'Outgoing Call' : 'Incoming Call';

                $row->agent_name = $row->agent_name;
                $row->action = '
                <a href="https://pbx.voxbaysolutions.com/callrecordings/' . str_replace(" ", "+", $rec_url) . '" class="btn btn-info" title="Download Record Audio" download ><i class="fa fa-download mg-r-5"></i></a>';
            }

            $row->audio = $row->recording_url ? '<div class="col-md-6"> <audio   controls controlsList="nodownload noplaybackrate"><source src="' . $rec_url . '" type="audio/wav"/></audio> </div>' : 'Not Applicable';
        }
        if ($request->export) {
            return view('backend.user-pages.ivr.excel', compact('data'));
        }

        return [
            'draw' => $draw,
            'recordsTotal' => $data->total(),
            'recordsFiltered' => $data->total(),
            'data' => $data
        ];

    }

    public function downloadIvr($id, $flag)
    {
        if ($flag == 2) {
            $url = CallMaster::where('id', $id)->select('recording_url')->first();
            $file = $url->recording_url;
        } else {
            $url = Ivr::where('id', $id)->select('recording_url')->first();
            $file = "https://pbx.voxbaysolutions.com/callrecordings/" . str_replace(" ", "+", $url->recording_url);
        }

        return Response::download($file);
    }

    public function updatename(Request $request, $id)
    {
        $ivr = Ivr::where('fk_int_enquiry_id', $id)->where('vendor_id', User::getVendorId());
        $enquiry = Enquiry::find($id);

        if ($enquiry) {
            $enquiry->vchr_customer_name = $request->name;
            $enquiry->save();

            if ($ivr->count() > 0) {
                $ivr->update(['caller_name' => $request->name]);
            }

            return response()->json([
                'status' => 'success',
                'msg' => 'Updated'
            ]);
        } else {
            return response()->json([
                'status' => 'fail',
                'msg' => 'Not exist'
            ]);
        }
    }

    public function transfer(Request $request)
    {
        $routings = IvrTransfer::with(['virtual_number'])
            ->whereHas('virtual_number', function ($query) {
                $query->where('fk_int_user_id', User::getVendorId());
            })->active()
            ->get();
        return view('backend.user-pages.ivr.ivr-routing', compact('routings'));
    }

    public function transferLog(Request $request)
    {
        $routings = IvrTransferLog::where('user_id', User::getVendorId())->orderBy('created_at', 'DESC')->get();
        return view('backend.user-pages.ivr.ivr-routing-log', compact('routings'));
    }

    public function updatetransfer(Request $request)
    {

    }

    public function routing(Request $request)
    {
        $validate_fields = [
            'username' => ['required'],
            'token' => 'required',
            'agent_number' => ['required', 'numeric'],
            'recipient_number' => ['required', 'numeric'],
            'did_number' => ['required', 'numeric'],
            'action' => 'required',
        ];

        $validation = Validator::make($request->all(), $validate_fields);
        if ($validation->fails()) {
            $response = [
                'status' => 'fail',
                'message' => $validation->errors()->first()
            ];
            return response()->json($response, 200);
        } else {
            $check_token = GlApiTokens::where('vchr_token', $request->token)->first();
            if (!empty($check_token)) {
                $userId = $check_token->fk_int_user_id;
                $userDetails = User::where('pk_int_user_id', $userId)
                    ->where('vchr_user_mobile', $request->username)
                    ->Orwhere('pk_int_user_id', $userId)
                    ->where('email', $request->username)->first();
                if ($userDetails) {
                    $virtualnumber = VirtualNumber::where([
                        'fk_int_user_id' => $userId,
                        'vchr_virtual_number' => $request->did_number
                    ])->first();
                    if ($virtualnumber) {
                        if ($request->action == 'add') {
                            $itransfer = IvrTransfer::where('agent_number', str_replace("+", "", $request->agent_number))->where('fk_int_virtual_number_id', $virtualnumber->pk_int_virtual_number_id)->first();
                            if (!$itransfer) {
                                $transfer = new IvrTransfer;
                                $transfer->fk_int_virtual_number_id = $virtualnumber->pk_int_virtual_number_id;
                            } else {
                                $transfer = IvrTransfer::find($itransfer->id);
                            }

                            $transfer->agent_number = str_replace("+", "", $request->agent_number);
                            $transfer->customer_number = str_replace("+", "", $request->recipient_number);
                            $transfer->customer_name = $request->recipient_name;
                            if ($request->expire_in && $request->expire_in != '')
                                $transfer->expire_in = $request->expire_in;
                            $transfer->updated_at = Carbon::now()->format('Y-m-d H:i:s');
                            $transfer->save();
                            //Create Log
                            $transfer_log = new IvrTransferLog;
                            $transfer_log->user_id = $userId;
                            $transfer_log->virtual_number = $request->did_number;
                            $transfer_log->agent_number = str_replace("+", "", $request->agent_number);
                            $transfer_log->customer_number = str_replace("+", "", $request->recipient_number);
                            $transfer_log->customer_name = $request->recipient_name;
                            $transfer_log->status = 0;
                            if ($request->expire_in && $request->expire_in != '')
                                $transfer_log->expire_at = Carbon::now()->addMinutes($request->expire_in)->format('Y-m-d- H:i:s');
                            $transfer_log->save();
                            $transfer_log->track_id = 'GLITR000' . $transfer_log->id;
                            $transfer_log->save();
                            //
                        } elseif ($request->action == 'remove') {
                            if (IvrTransfer::where('agent_number', str_replace("+", "", $request->agent_number))->where('fk_int_virtual_number_id', $virtualnumber->pk_int_virtual_number_id)->count() > 0)
                                IvrTransfer::where('agent_number', str_replace("+", "", $request->agent_number))->where('fk_int_virtual_number_id', $virtualnumber->pk_int_virtual_number_id)->delete();
                            else
                                return response()->json(['message' => 'No matching data', 'status' => 'fail']);
                            return response()->json(['message' => 'Success', 'status' => 'success']);
                        } else
                            return response()->json(['message' => 'Invalid Action', 'status' => 'fail']);
                        return response()->json(['message' => 'Success', 'status' => 'success', 'track_id' => $transfer_log->track_id]);
                    } else
                        return response()->json(['message' => 'DID Number Not Found', 'status' => 'fail']);
                } else
                    return response()->json(['message' => 'User Not Found', 'status' => 'fail']);
            } else
                return response()->json(['message' => 'Invalid Token', 'status' => 'fail']);

        }
        abort(401);
    }

    public function numbers(Request $request)
    {
        $numbers = VirtualNumber::where([
            'fk_int_user_id' => User::getVendorId(),
            'type' => 'IVR'
        ])->get();
        return view('backend.user-pages.ivr.ivr-numbers', compact('numbers'));
    }

    public function deleteVirtualNumber($ivrId)
    {
        $numbers = VirtualNumber::find($ivrId)->delete();

        $response = [
            'status' => 'success',
            'message' => 'Virtual number deleted successful'
        ];

        return response()->json($response, 200);
    }

    public function virtualNumberChangeStatus()
    {
        if (request('status') == "true") {
            VirtualNumber::find(request('id'))->update(['int_status' => 1]);
        } else {
            VirtualNumber::find(request('id'))->update(['int_status' => 0]);
        }

        $response = [
            'status' => 'success',
            'message' => 'Status updated successful'
        ];

        return response()->json($response, 200);
    }

    public function assignAgent(Request $request)
    {
        $number = VirtualNumber::where([
            'fk_int_user_id' => User::getVendorId(),
            'type' => 'IVR',
            'pk_int_virtual_number_id' => $request->id
        ])->first();
        if ($number) {
            $virtual_number = VirtualNumber::find($request->id);
            $virtual_number->agent_id = $request->agent_id;
            $virtual_number->save();
            return response()->json([
                'status' => 'success',
                'msg' => 'Updated'
            ]);
        } else
            return response()->json([
                'status' => 'fail',
                'msg' => 'Not exist'
            ]);
    }

    public function assignGroup(Request $request)
    {
        $number = VirtualNumber::where([
            'fk_int_user_id' => User::getVendorId(),
            'type' => 'IVR',
            'pk_int_virtual_number_id' => $request->id
        ])->first();
        if ($number) {
            $virtual_number = VirtualNumber::find($request->id);
            $virtual_number->lead_group_id = $request->group_id;
            $virtual_number->save();
            return response()->json([
                'status' => 'success',
                'msg' => 'Updated'
            ]);
        } else
            return response()->json([
                'status' => 'fail',
                'msg' => 'Not exist'
            ]);
    }

    public function extensions(Request $request, $id)
    {
        $extensions = IvrExtension::where([
            'vendor_id' => User::getVendorId(),
            'virtual_number_id' => $id
        ])->get();
        return view('backend.user-pages.ivr.ivr-extensions', compact('extensions', 'id'));
    }

    public function extension(Request $request)
    {
        $vendorId = User::getVendorId();
        $number = VirtualNumber::where([
            'fk_int_user_id' => $vendorId,
            'type' => 'IVR',
            'pk_int_virtual_number_id' => $request->ivr_id
        ])->first();
        if ($number) {
            $user = User::find($request->agent_id);
            if ($request->edit_id) {
                $extension = IvrExtension::where([
                    'vendor_id' => $vendorId,
                    'virtual_number_id' => $request->ivr_id,
                    'id' => $request->edit_id
                ])->first();
                if (!$extension)
                    return response()->json([
                        'status' => 'fail',
                        'msg' => 'Not exist'
                    ]);
                $extension = IvrExtension::find($request->edit_id);
                $extension->staff_id = $request->agent_id;
                $extension->extension = $request->extension;
                $extension->save();

                $number->agent_id = $request->agent_id;
                $number->save();

                if ($user) {
                    $user->extension = $request->extension;
                    $user->save();
                }

                return response()->json([
                    'status' => 'success',
                    'msg' => 'Updated'
                ]);
            } else {
                $extension = new IvrExtension;
                $extension->vendor_id = $vendorId;
                $extension->virtual_number_id = $request->ivr_id;
                $extension->staff_id = $request->agent_id;
                $extension->extension = $request->extension;
                $extension->save();

                if ($user) {
                    $user->extension = $request->extension;
                    $user->save();
                }

                return response()->json([
                    'status' => 'success',
                    'msg' => 'Added'
                ]);
            }

        } else
            return response()->json([
                'status' => 'fail',
                'msg' => 'Not exist'
            ]);
    }

    public function deleteExtension(Request $request, $id)
    {
        $extension = IvrExtension::where([
            'vendor_id' => User::getVendorId(),
            'id' => $id
        ])->first();
        if (!$extension)
            return response()->json([
                'status' => 'fail',
                'msg' => 'Not exist'
            ]);

        $ivr = IvrExtension::where([
            'id' => $id
        ])->first();

        if ($ivr) {
            $user = User::find($ivr->staff_id);
            if ($user) {
                $user->extension = NULL;
                $user->save();
            }
            $ivr->delete();
        }

        return response()->json([
            'status' => 'success',
            'msg' => 'Deleted'
        ]);

    }

    public function call_verify(Request $request)
    {
        $check_token = GlApiTokens::where('vchr_token', $request->token)->first();
        $vendor_id = User::getVendorIdApi($check_token->fk_int_user_id);
        if (!empty($check_token)) {
            $exist = Enquiry::where([
                'fk_int_user_id' => $vendor_id,
                'vchr_customer_mobile' => $request->caller_number
            ])->first();
            $checkDid = substr($request->did_number, -10);
            $virtualnumber = VirtualNumber::where('fk_int_user_id', $vendor_id)
                ->where('vchr_virtual_number', 'LIKE', '%' . $checkDid . '%')
                ->first();
            if (request()->has('flag') && request('flag') == 'BONVOICE') {
                if ($virtualnumber) {
                    if ($exist) {
                        $staff = User::select('mobile')->find($exist->staff_id);
                        $staff_number = $staff ? $staff->mobile : '';

                        return response()->json(['app_name' => 'OUTBOUND', 'app_value' => $staff_number]);
                    } else
                        return response()->json(['error' => 'NO DESTINATION FOUND']);
                } else {
                    return response()->json(['error' => 'DID number not Found']);
                }
            } else {
                if ($virtualnumber) {
                    if ($exist) {
                        $staff = User::select('mobile')->find($exist->staff_id);
                        $staff_number = $staff ? $staff->mobile : '';
                        // $did=$numbers?$numbers->vchr_virtual_number:'';
                        return $staff_number;
                        //  return response()->json(['agent_number'=>$staff_number,'did'=>$did]);
                    } else
                        return '';
                    // return response()->json(['message'=>'Please check the Number', 'status' => 'fail']);
                } else {
                    return 'DID number not Found';
                }
            }
        } else {
            \Log::info('Please check the parameters');

            return response()->json(['message' => 'Please check the parameters', 'status' => 'fail']);
        }
    }

    private function resolveVoxBayRecordingUrl(string $recordingUrl): string
    {
        return strpos($recordingUrl, 'https://') === 0
            ? $recordingUrl
            : 'https://pbx.voxbaysolutions.com/callrecordings/' . str_replace(" ", "+", $recordingUrl);
    }
}

