<?php

declare(strict_types=1);

namespace App\GLDialer\Http\Controllers;

use App\GLDialer\Http\Requests\CallSyncRequest;
use App\Ivr\Enums\IvrEvent;
use App\Ivr\Enums\Provider;
use App\Ivr\IVRWebhook\Jobs\ProcessCallEventPush;
use App\Ivr\Models\CallEventPush;
use App\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

final class CallSyncController
{
    public function __invoke(CallSyncRequest $request): JsonResponse
    {
        Log::info('Call sync request received from GL Dialer', [
            'request' => $request->all(),
        ]);

        /** @var User $user */
        $user = Auth::guard('api')->user();
        $vendorId = $user->getBusinessId();
        $userId = $user->pk_int_user_id;

        $now = now();

        foreach ($request->validated() as $callData) {
            $callEventPush = CallEventPush::query()
                ->create([
                    'vendor_id' => $vendorId,
                    'external_id' => $callData['external_id'],
                    'provider' => Provider::GLDialer,
                    'event' => $this->resolveEventType($callData['event']),
                    'payload' => $callData['payload'],
                    'created_at' => $now,
                ]);

            Bus::dispatch(new ProcessCallEventPush(ivrEventPush: $callEventPush));

            Log::info('GL Dialer Call event pushed to queue for processing', [
                'ivr_event_push_id' => $callEventPush->id,
                'event' => $callEventPush->event,
                'external_id' => $callData['external_id'],
                'vendor_id' => $vendorId,
                'user_id' => $userId,
            ]);
        }

        return response()->json([], 202);
    }

    private function resolveEventType(string $eventType): IvrEvent
    {
        return match ($eventType) {
            'missed' => IvrEvent::Missed,
            'answered' => IvrEvent::Answered,
            'outgoing' => IvrEvent::Outgoing,
            'incoming' => IvrEvent::Incoming,
            default => IvrEvent::Unknown,
        };
    }
}
