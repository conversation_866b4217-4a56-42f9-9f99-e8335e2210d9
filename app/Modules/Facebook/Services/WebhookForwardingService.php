<?php

declare(strict_types=1);

namespace App\Modules\Facebook\Services;

use App\Modules\Facebook\Jobs\ForwardWebhookJob;
use App\Modules\Facebook\Models\FacebookWebhookForwardUrl;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

final class WebhookForwardingService
{
    public function forwardWebhook(Request $request): void
    {
        /** @var string[] $forwardUrls */
        $forwardUrls = Cache::remember('facebook_webhook_forward_urls', now()->addMonth(), function () {
            return $this->getForwardUrls();
        });

        if ($forwardUrls === []) {
            Log::info('Facebook webhook forward URLs not configured, skipping forwarding');
            return;
        }

        $headers = [
            'X-Forwarded-From' => config('app.url'),
            'X-Original-Host' => $request->getHost(),
        ];

        $webhookData = $request->all();

        Log::info('Dispatching Facebook webhook forwarding jobs', [
            'forward_urls' => $forwardUrls,
            'url_count' => count($forwardUrls),
        ]);

        foreach ($forwardUrls as $url) {
            dispatch(new ForwardWebhookJob(forwardUrl: $url, webhookData: $webhookData, headers: $headers));
        }
    }

    private function getForwardUrls(): Collection
    {
        return FacebookWebhookForwardUrl::query()
            ->where('status','=', true)
            ->pluck('url');

    }
}
