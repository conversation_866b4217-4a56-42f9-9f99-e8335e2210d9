<?php

declare(strict_types=1);

namespace App\Modules\Facebook\Models;

use Illuminate\Database\Eloquent\Model;

class FacebookWebhookForwardUrl extends Model
{
    protected $table = 'facebook_webhook_forward_urls';

    protected $fillable = [
        'name',
        'url',
        'status',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    public function scopeActive($query)
    {
        return $query->where('status', true);
    }
}
